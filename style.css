/* Reset and base styles */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: linear-gradient(135deg, #1d2023 0%, #2a2d32 100%);
  color: #fff;
  min-height: 100vh;
  line-height: 1.6;
}

/* Accessibility helper */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Container and layout */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem 1rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header styles */
header {
  text-align: center;
  margin-bottom: 3rem;
}

h1 {
  color: #fff;
  font-size: clamp(2rem, 5vw, 3rem);
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  color: #b0b3b8;
  font-size: 1.1rem;
  font-weight: 300;
}

/* Input section */
.input-section {
  width: 100%;
  max-width: 600px;
  margin-bottom: 3rem;
}

.prompt {
  background-color: #292c31;
  padding: 8px;
  width: 100%;
  border: 3px solid #36383d;
  border-radius: 12px;
  display: flex;
  gap: 8px;
  transition: border-color 0.3s ease;
}

.prompt:focus-within {
  border-color: #4a9eff;
}

input {
  font-family: inherit;
  font-size: 16px;
  padding: 12px 16px;
  border: none;
  color: #fff;
  background-color: transparent;
  flex: 1;
  outline: none;
  border-radius: 8px;
}

input::placeholder {
  color: #8a8d93;
}

button {
  font-family: inherit;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  border: none;
  color: #fff;
  background: linear-gradient(135deg, #4a9eff 0%, #0066cc 100%);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

button:hover:not(:disabled) {
  background: linear-gradient(135deg, #5aa7ff 0%, #0077dd 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 158, 255, 0.3);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.input-help {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #8a8d93;
  text-align: center;
}

/* Results section */
.results-section {
  width: 100%;
  max-width: 800px;
}

.images {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  justify-items: center;
}

.image-placeholder,
.image-container {
  height: 250px;
  width: 250px;
  border-radius: 12px;
  border: 3px solid #36383d;
  background-color: #1a1d20;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  transition: all 0.3s ease;
}

.image-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

/* Loading and error states */
.loading {
  color: #4a9eff;
  font-weight: 500;
  text-align: center;
  padding: 2rem;
  font-size: 1.1rem;
}

.error {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  border: 2px solid rgba(255, 107, 107, 0.3);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  font-weight: 500;
}

.image-error {
  color: #ff6b6b;
  font-size: 0.9rem;
  text-align: center;
  padding: 1rem;
}

/* Footer */
footer {
  margin-top: auto;
  padding: 2rem 1rem 1rem;
  text-align: center;
  color: #8a8d93;
  font-size: 0.9rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .prompt {
    flex-direction: column;
    gap: 12px;
  }

  button {
    width: 100%;
    padding: 14px;
  }

  .images {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .image-placeholder,
  .image-container {
    width: 100%;
    max-width: 300px;
    height: 300px;
  }
}

@media (max-width: 480px) {
  .image-placeholder,
  .image-container {
    height: 250px;
  }

  h1 {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}
