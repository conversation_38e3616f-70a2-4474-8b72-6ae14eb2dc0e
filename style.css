*{
    padding: 0;
    margin: 0;
    font-family: '<PERSON><PERSON><PERSON>', sans-serif;
}
body{
    background-color:  "#1d2023";
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 100vh;
}
h2{
    color: #fff;
    font-size: 40px;
    margin-bottom: 2rem;
}
.prompt{
    background-color: #292c31;
    padding: 5px;
    width: 620ox;
    border: 3px solid #36383d;
    border-radius: 10px;
}
input,button{
    font-size: 20px;
    padding: 10px;
    border: none;
    color: #fff;
}
input{
    background-color: #292c31;
    width: 75%;
    outline: none;
  
}
button{
    background-color: #36383d;
    width: 100%;
    cursor: pointer;
}
button:hover{
    background-color: #444;
}
.images{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}
    
.images div{
    height: 200px;
    width: 200px;
    border-radius: 10px;
    border: 3px solid #36383d;
}
.images div img{
    width: 100%;
    
}
