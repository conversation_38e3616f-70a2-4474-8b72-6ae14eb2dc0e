# AI Image Generator

A modern, responsive web application that generates images using OpenAI's DALL-E API. Create stunning AI-generated images with simple text descriptions.

## Features

- 🎨 Generate high-quality images using AI
- 📱 Fully responsive design
- ♿ Accessible interface with ARIA labels
- 🔄 Real-time loading states
- ❌ Comprehensive error handling
- 🎯 Input validation and feedback
- 🖼️ Generate up to 3 images simultaneously

## Prerequisites

- A modern web browser
- OpenAI API key (get one at [OpenAI Platform](https://platform.openai.com/))

## Setup Instructions

### 1. Get Your OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Create a new API key
5. Copy the key (keep it secure!)

### 2. Configure the Application

**Option A: Direct Configuration (for testing only)**
1. Open `main.js`
2. Find the `CONFIG` object at the top
3. Replace the empty `API_KEY` value with your actual API key:
   ```javascript
   const CONFIG = {
       API_KEY: 'your-api-key-here',
       // ... other settings
   };
   ```

**Option B: Environment Variables (recommended for production)**
1. Set up a local server environment
2. Set the `OPENAI_API_KEY` environment variable
3. The application will automatically use it

### 3. Run the Application

1. Open `index.html` in your web browser
2. Enter a description of the image you want to generate
3. Click "Generate" or press Enter
4. Wait for the AI to create your images!

## Usage Tips

- Be descriptive in your prompts for better results
- Try different artistic styles (e.g., "in the style of Van Gogh")
- Specify details like lighting, colors, and composition
- Keep descriptions under 1000 characters

## Example Prompts

- "A serene mountain landscape at sunset with a crystal clear lake"
- "A futuristic city skyline with flying cars, cyberpunk style"
- "A cute golden retriever puppy playing in a field of sunflowers"
- "An abstract painting with vibrant blues and purples, geometric shapes"

## File Structure

```
ai-image-generator/
├── index.html          # Main HTML file
├── style.css           # Styles and responsive design
├── main.js             # JavaScript functionality
├── config.js           # Configuration settings
└── README.md           # This file
```

## Security Notes

⚠️ **IMPORTANT**: Never commit your API key to version control!

- Use environment variables for production
- Consider using a backend service to proxy API calls
- Rotate your API keys regularly
- Monitor your API usage and costs

## Browser Compatibility

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Troubleshooting

### "API key not configured" error
- Make sure you've set your OpenAI API key in the configuration
- Check that the key is valid and has sufficient credits

### Images not loading
- Check your internet connection
- Verify your API key has the necessary permissions
- Check the browser console for detailed error messages

### Responsive issues
- Clear your browser cache
- Ensure you're using a supported browser version

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your API key configuration
3. Ensure you have sufficient OpenAI API credits
4. Check your internet connection

---

**Powered by OpenAI's DALL-E API** 🤖✨
