/**
 * Configuration file for AI Image Generator
 *
 * SECURITY NOTE: Never commit your actual API key to version control!
 * Use environment variables or a secure configuration management system.
 */

// Configuration object
const CONFIG = {
  // OpenAI API Configuration
  API_KEY: "PUT_YOUR_OPENAI_API_KEY_HERE", // Replace this with your actual OpenAI API key
  API_URL: "https://api.openai.com/v1/images/generations",

  // Image generation settings
  IMAGE_COUNT: 3,
  IMAGE_SIZE: "1024x1024", // Options: '256x256', '512x512', '1024x1024'

  // UI Configuration
  MAX_PROMPT_LENGTH: 1000,
  PLACEHOLDER_TEXT: "Describe the image you want to generate...",

  // Error messages
  MESSAGES: {
    NO_API_KEY:
      "API key not configured. Please set your OpenAI API key in the configuration.",
    EMPTY_PROMPT:
      "Please enter a description for the image you want to generate.",
    PROMPT_TOO_LONG: "Please keep your description under 1000 characters.",
    GENERATION_ERROR: "Failed to generate images. Please try again.",
    IMAGE_LOAD_ERROR: "Failed to load image",
  },
};

// Export for use in other files (if using modules)
if (typeof module !== "undefined" && module.exports) {
  module.exports = CONFIG;
}

// Make available globally for browser usage
if (typeof window !== "undefined") {
  window.CONFIG = CONFIG;
}
