const api = "********************************************************************************************************************************************************************";

const input = document.getElementById("input");
const images = document.querySelector(".images");

const getImage = async () => {
    //request to openAI
    const response = await fetch("https://api.openai.com/v1/images/generations", {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${api}`
        },
        body: JSON.stringify({
            "prompt": input.value,
            "n": 3,
            "size": "1024x1024"
        })
    });
    //get data from response
    const data = await response.json();
    console.log(data);
    //display images
    images.innerHTML = "";
    data.data.forEach(image => {
        const img = document.createElement("img");
        img.src = image.url;
        images.appendChild(img);
    });

}
