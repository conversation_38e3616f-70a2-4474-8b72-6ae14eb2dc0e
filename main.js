// Import configuration (will use window.CONFIG if available, otherwise fallback)
const CONFIG = window.CONFIG || {
  API_KEY: "", // Set this via environment variables or secure configuration
  API_URL: "https://api.openai.com/v1/images/generations",
  IMAGE_COUNT: 3,
  IMAGE_SIZE: "1024x1024",
  MAX_PROMPT_LENGTH: 1000,
  MESSAGES: {
    NO_API_KEY:
      "API key not configured. Please set your OpenAI API key in the configuration.",
    EMPTY_PROMPT:
      "Please enter a description for the image you want to generate.",
    PROMPT_TOO_LONG: "Please keep your description under 1000 characters.",
    GENERATION_ERROR: "Failed to generate images. Please try again.",
    IMAGE_LOAD_ERROR: "Failed to load image",
  },
};

// DOM elements
const input = document.getElementById("input");
const images = document.querySelector(".images");
const generateButton = document.querySelector("button");
const loadingIndicator = document.createElement("div");

// Initialize loading indicator
loadingIndicator.className = "loading";
loadingIndicator.textContent = "Generating images...";
loadingIndicator.style.display = "none";

/**
 * Validates user input before making API request
 * @param {string} prompt - User input prompt
 * @returns {boolean} - Whether input is valid
 */
const validateInput = (prompt) => {
  if (!prompt || prompt.trim().length === 0) {
    alert(CONFIG.MESSAGES.EMPTY_PROMPT);
    return false;
  }
  if (prompt.trim().length > CONFIG.MAX_PROMPT_LENGTH) {
    alert(CONFIG.MESSAGES.PROMPT_TOO_LONG);
    return false;
  }
  return true;
};

/**
 * Shows loading state
 */
const showLoading = () => {
  generateButton.disabled = true;
  generateButton.textContent = "Generating...";
  images.appendChild(loadingIndicator);
  loadingIndicator.style.display = "block";
};

/**
 * Hides loading state
 */
const hideLoading = () => {
  generateButton.disabled = false;
  generateButton.textContent = "Generate";
  if (loadingIndicator.parentNode) {
    loadingIndicator.parentNode.removeChild(loadingIndicator);
  }
};

/**
 * Displays error message to user
 * @param {string} message - Error message to display
 */
const showError = (message) => {
  images.innerHTML = `<div class="error">Error: ${message}</div>`;
};

/**
 * Generates images using OpenAI API
 */
const getImage = async () => {
  const prompt = input.value.trim();

  // Validate input
  if (!validateInput(prompt)) {
    return;
  }

  // Check if API key is configured
  if (!CONFIG.API_KEY) {
    showError(CONFIG.MESSAGES.NO_API_KEY);
    return;
  }

  try {
    showLoading();

    // Request to OpenAI API
    const response = await fetch(CONFIG.API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${CONFIG.API_KEY}`,
      },
      body: JSON.stringify({
        prompt: prompt,
        n: CONFIG.IMAGE_COUNT,
        size: CONFIG.IMAGE_SIZE,
      }),
    });

    // Check if request was successful
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(
        errorData.error?.message || `HTTP error! status: ${response.status}`
      );
    }

    // Get data from response
    const data = await response.json();
    console.log("API Response:", data);

    // Display images
    displayImages(data.data);
  } catch (error) {
    console.error("Error generating images:", error);
    showError(error.message || CONFIG.MESSAGES.GENERATION_ERROR);
  } finally {
    hideLoading();
  }
};

/**
 * Displays generated images in the UI
 * @param {Array} imageData - Array of image objects from API
 */
const displayImages = (imageData) => {
  images.innerHTML = "";

  imageData.forEach((imageObj, index) => {
    const imgContainer = document.createElement("div");
    imgContainer.className = "image-container";

    const img = document.createElement("img");
    img.src = imageObj.url;
    img.alt = `Generated image ${index + 1}`;
    img.loading = "lazy";

    // Add error handling for image loading
    img.onerror = () => {
      imgContainer.innerHTML = `<div class="image-error">${CONFIG.MESSAGES.IMAGE_LOAD_ERROR}</div>`;
    };

    imgContainer.appendChild(img);
    images.appendChild(imgContainer);
  });
};

// Event listeners
document.addEventListener("DOMContentLoaded", () => {
  // Add click event to generate button
  generateButton.addEventListener("click", getImage);

  // Add enter key support for input field
  input.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      getImage();
    }
  });

  // Add input validation feedback
  input.addEventListener("input", () => {
    const prompt = input.value.trim();
    if (prompt.length > CONFIG.MAX_PROMPT_LENGTH) {
      input.style.borderColor = "#ff4444";
    } else {
      input.style.borderColor = "";
    }
  });
});
